/**
 * Test script to verify sidebar role-based access control
 * This simulates different user roles and checks which groups/items they can access
 */

// Mock the navigation groups structure (simplified version)
const navigationGroups = [
  {
    id: "core",
    label: "Core",
    roles: ["manager", "accountant", "hr-admin", "admin", "super-admin"],
    items: [
      {
        label: "Dashboard",
        roles: ["manager", "accountant", "hr-admin", "admin", "super-admin"]
      }
    ]
  },
  {
    id: "people",
    label: "People Management",
    roles: ["hr-admin", "admin", "super-admin"],
    items: [
      {
        label: "Employees",
        roles: ["hr-admin", "admin", "super-admin"]
      },
      {
        label: "Add People",
        roles: ["super-admin"]
      },
      {
        label: "Team",
        roles: ["super-admin"]
      },
      {
        label: "Departments",
        roles: ["hr-admin", "admin", "super-admin"]
      }
    ]
  },
  {
    id: "appraisals",
    label: "Appraisal System",
    roles: ["manager", "hr-admin", "admin", "super-admin"],
    items: [
      {
        label: "Approvals",
        roles: ["hr-admin", "super-admin"]
      },
      {
        label: "Multi-Level Approvals",
        roles: ["manager", "hr-admin", "admin", "super-admin"]
      },
      {
        label: "Appraisal Periods",
        roles: ["hr-admin", "admin", "super-admin"]
      }
    ]
  },
  {
    id: "financial",
    label: "Financial",
    roles: ["accountant", "super-admin"],
    items: [
      {
        label: "Accounting",
        roles: ["accountant", "super-admin"]
      }
    ]
  },
  {
    id: "time-benefits",
    label: "Time & Benefits",
    roles: ["manager", "accountant", "hr-admin", "admin", "super-admin"],
    items: [
      {
        label: "PTO",
        roles: ["manager", "accountant", "hr-admin", "admin", "super-admin"],
        requiresMarketingDept: true
      }
    ]
  },
  {
    id: "communication",
    label: "Communication",
    roles: ["manager", "accountant", "hr-admin", "admin", "super-admin"],
    items: [
      {
        label: "Feedback",
        roles: ["manager", "accountant", "hr-admin", "admin", "super-admin"]
      },
      {
        label: "HR Feedback",
        roles: ["hr-admin", "super-admin"]
      }
    ]
  },
  {
    id: "administration",
    label: "Administration",
    roles: ["super-admin"],
    items: [
      {
        label: "System Admin",
        roles: ["super-admin"]
      },
      {
        label: "Email Settings",
        roles: ["super-admin"]
      }
    ]
  }
]

// Test users with different roles
const testUsers = [
  {
    id: "user1",
    fullName: "Manager User",
    role: "manager",
    department: "Sales"
  },
  {
    id: "user2", 
    fullName: "Accountant User",
    role: "accountant",
    department: "Finance"
  },
  {
    id: "user3",
    fullName: "HR Admin User", 
    role: "hr-admin",
    department: "HR"
  },
  {
    id: "user4",
    fullName: "Admin User",
    role: "admin", 
    department: "IT"
  },
  {
    id: "user5",
    fullName: "Super Admin User",
    role: "super-admin",
    department: "Management"
  },
  {
    id: "user6",
    fullName: "Natalie Marketing",
    role: "manager",
    department: "Marketing"
  }
]

// Helper functions (simplified versions of the actual functions)
function hasAccess(userRole, allowedRoles) {
  return allowedRoles.includes(userRole)
}

function filterGroupItems(group, user) {
  return group.items.filter(item => {
    const hasRoleAccess = hasAccess(user.role, item.roles)
    
    // For PTO, only show to Marketing department users or super-admins
    if (item.requiresMarketingDept) {
      const isSuperAdmin = user.role === 'super-admin'
      
      if (isSuperAdmin) {
        return hasRoleAccess
      }
      
      const isMarketingUser = user.fullName?.toLowerCase().includes('natalie') ||
                              user.id === 'user_natalie'
      return hasRoleAccess && isMarketingUser
    }
    
    return hasRoleAccess
  })
}

function getAccessibleGroups(user) {
  return navigationGroups.map(group => {
    const accessibleItems = filterGroupItems(group, user)

    if (accessibleItems.length > 0 && hasAccess(user.role, group.roles)) {
      return {
        ...group,
        items: accessibleItems
      }
    }
    return null
  }).filter(Boolean)
}

// Run tests
console.log('🧪 SIDEBAR ROLE-BASED ACCESS CONTROL TESTS\n')

testUsers.forEach(user => {
  console.log(`👤 Testing user: ${user.fullName} (${user.role})`)
  console.log(`   Department: ${user.department}`)
  
  const accessibleGroups = getAccessibleGroups(user)
  
  console.log(`   Accessible groups: ${accessibleGroups.length}`)
  
  accessibleGroups.forEach(group => {
    console.log(`   📁 ${group.label} (${group.items.length} items)`)
    group.items.forEach(item => {
      console.log(`      - ${item.label}`)
    })
  })
  
  console.log('')
})

// Expected results validation
console.log('✅ EXPECTED RESULTS VALIDATION\n')

const expectations = {
  'manager': {
    groups: ['Core', 'Appraisal System', 'Communication'],
    shouldNotSee: ['People Management', 'Financial', 'Administration']
  },
  'accountant': {
    groups: ['Core', 'Financial', 'Communication'],
    shouldNotSee: ['People Management', 'Appraisal System', 'Administration']
  },
  'hr-admin': {
    groups: ['Core', 'People Management', 'Appraisal System', 'Communication'],
    shouldNotSee: ['Financial', 'Administration', 'Time & Benefits']
  },
  'admin': {
    groups: ['Core', 'People Management', 'Appraisal System', 'Communication'],
    shouldNotSee: ['Financial', 'Administration', 'Time & Benefits']
  },
  'super-admin': {
    groups: ['Core', 'People Management', 'Appraisal System', 'Financial', 'Time & Benefits', 'Communication', 'Administration'],
    shouldNotSee: []
  }
}

testUsers.forEach(user => {
  const accessibleGroups = getAccessibleGroups(user)
  const groupNames = accessibleGroups.map(g => g.label)
  const expected = expectations[user.role]
  
  if (expected) {
    console.log(`👤 ${user.fullName} (${user.role}):`)
    
    // Check expected groups
    const missingGroups = expected.groups.filter(g => !groupNames.includes(g))
    if (missingGroups.length === 0) {
      console.log(`   ✅ Has all expected groups`)
    } else {
      console.log(`   ❌ Missing groups: ${missingGroups.join(', ')}`)
    }
    
    // Check groups that shouldn't be visible
    const unexpectedGroups = groupNames.filter(g => expected.shouldNotSee.includes(g))
    if (unexpectedGroups.length === 0) {
      console.log(`   ✅ No unexpected groups visible`)
    } else {
      console.log(`   ❌ Unexpected groups visible: ${unexpectedGroups.join(', ')}`)
    }
    
    console.log('')
  }
})

console.log('🎯 Test completed!')
