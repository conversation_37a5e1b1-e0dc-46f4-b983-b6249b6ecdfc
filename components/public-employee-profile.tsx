"use client"

import { Employee } from "@/lib/types"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Building, 
  Linkedin,
  Twitter,
  MessageCircle,
  Briefcase,
  Users
} from "lucide-react"

interface PublicEmployeeProfileProps {
  employee: Employee
}

export function PublicEmployeeProfile({ employee }: PublicEmployeeProfileProps) {
  return (
    <div className="max-w-2xl mx-auto px-4">
      <Card>
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-3xl font-bold">
            {employee.firstName} {employee.lastName}
          </CardTitle>
          {employee.role && (
            <p className="text-lg text-muted-foreground mt-2">
              {employee.role}
            </p>
          )}
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Department */}
          <div className="flex items-center justify-center gap-3">
            <Building className="h-5 w-5 text-muted-foreground" />
            <span className="text-base font-medium">
              {employee.departmentName || 'No Department'}
            </span>
          </div>

          {/* Manager(s) */}
          {employee.managers && employee.managers.length > 0 && (
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center gap-2 text-muted-foreground">
                <Users className="h-5 w-5" />
                <span className="text-sm">Reports to</span>
              </div>
              <div className="flex flex-wrap justify-center gap-2">
                {employee.managers.map((manager) => (
                  <Badge key={manager.id} variant="outline" className="text-sm">
                    {manager.managerName}
                    {manager.isPrimary && " (Primary)"}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Social Links */}
          {(employee.linkedinUrl || employee.twitterUrl || employee.telegramUrl) && (
            <div className="flex justify-center gap-6 pt-4">
              {employee.linkedinUrl && (
                <a 
                  href={employee.linkedinUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
                  aria-label="LinkedIn Profile"
                >
                  <Linkedin className="h-6 w-6" />
                </a>
              )}
              {employee.twitterUrl && (
                <a 
                  href={employee.twitterUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-sky-500 hover:text-sky-700 transition-colors"
                  aria-label="Twitter Profile"
                >
                  <Twitter className="h-6 w-6" />
                </a>
              )}
              {employee.telegramUrl && (
                <a 
                  href={employee.telegramUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-blue-500 hover:text-blue-700 transition-colors"
                  aria-label="Telegram Profile"
                >
                  <MessageCircle className="h-6 w-6" />
                </a>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}