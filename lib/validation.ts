/**
 * Validation utilities for employee profile data
 * Includes URL validation and text sanitization
 */

/**
 * Validates if a URL is a valid LinkedIn profile URL
 */
export function isValidLinkedInUrl(url: string | null | undefined): boolean {
  if (!url || url.trim() === '') return true // Allow empty URLs
  
  try {
    const urlObj = new URL(url)
    return urlObj.hostname === 'linkedin.com' || 
           urlObj.hostname === 'www.linkedin.com' ||
           (urlObj.hostname.endsWith('.linkedin.com') && urlObj.pathname.startsWith('/in/'))
  } catch {
    return false
  }
}

/**
 * Validates if a URL is a valid Twitter/X profile URL
 */
export function isValidTwitterUrl(url: string | null | undefined): boolean {
  if (!url || url.trim() === '') return true // Allow empty URLs
  
  try {
    const urlObj = new URL(url)
    return urlObj.hostname === 'twitter.com' || 
           urlObj.hostname === 'www.twitter.com' ||
           urlObj.hostname === 'x.com' ||
           urlObj.hostname === 'www.x.com'
  } catch {
    return false
  }
}

/**
 * Validates if a URL is a valid Telegram profile URL
 */
export function isValidTelegramUrl(url: string | null | undefined): boolean {
  if (!url || url.trim() === '') return true // Allow empty URLs
  
  try {
    const urlObj = new URL(url)
    return urlObj.hostname === 't.me' || 
           urlObj.hostname === 'www.t.me' ||
           urlObj.hostname === 'telegram.me' ||
           urlObj.hostname === 'www.telegram.me'
  } catch {
    return false
  }
}

/**
 * Validates a general URL format
 */
export function isValidUrl(url: string | null | undefined): boolean {
  if (!url || url.trim() === '') return true // Allow empty URLs
  
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

/**
 * Sanitizes bio text to prevent XSS attacks
 * Strips HTML tags and encodes special characters
 */
export function sanitizeBio(bio: string | null | undefined): string | null {
  if (!bio || bio.trim() === '') return null
  
  // Remove HTML tags
  const htmlStripped = bio.replace(/<[^>]*>/g, '')
  
  // Encode special characters to prevent XSS
  const encoded = htmlStripped
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
  
  // Limit length for security and UX
  const maxLength = 1000
  return encoded.length > maxLength 
    ? encoded.substring(0, maxLength).trim() + '...'
    : encoded.trim()
}

/**
 * Validates email format
 */
export function isValidEmail(email: string | null | undefined): boolean {
  if (!email || email.trim() === '') return false
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validates and sanitizes all profile data
 */
export function validateProfileData(data: {
  firstName?: string
  lastName?: string
  email?: string
  role?: string
  bio?: string
  linkedinUrl?: string
  twitterUrl?: string
  telegramUrl?: string
}): {
  isValid: boolean
  errors: string[]
  sanitizedData: {
    first_name: string
    last_name: string
    email: string | null
    role: string | null
    bio: string | null
    linkedin_url: string | null
    twitter_url: string | null
    telegram_url: string | null
  }
} {
  const errors: string[] = []
  
  // Validate required fields
  if (!data.firstName || data.firstName.trim() === '') {
    errors.push('First name is required')
  }
  
  if (!data.lastName || data.lastName.trim() === '') {
    errors.push('Last name is required')
  }
  
  // Validate email if provided
  if (data.email && !isValidEmail(data.email)) {
    errors.push('Invalid email format')
  }
  
  // Validate URLs
  if (data.linkedinUrl && !isValidLinkedInUrl(data.linkedinUrl)) {
    errors.push('Invalid LinkedIn URL format')
  }
  
  if (data.twitterUrl && !isValidTwitterUrl(data.twitterUrl)) {
    errors.push('Invalid Twitter/X URL format')
  }

  if (data.telegramUrl && !isValidTelegramUrl(data.telegramUrl)) {
    errors.push('Invalid Telegram URL format')
  }
  
  // Sanitize and prepare data
  const sanitizedData = {
    first_name: data.firstName?.trim() || '',
    last_name: data.lastName?.trim() || '',
    email: data.email?.trim() || null,
    role: data.role?.trim() || null,
    bio: sanitizeBio(data.bio),
    linkedin_url: data.linkedinUrl?.trim() || null,
    twitter_url: data.twitterUrl?.trim() || null,
    telegram_url: data.telegramUrl?.trim() || null,
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitizedData
  }
}