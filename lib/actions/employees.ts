"use server"

import { revalidatePath } from "next/cache"
import { saveEmployee } from "../data/index"
import { employeeFormSchema } from "../schemas"
import { db } from "../db"
import { supabaseAdminQuery } from "../supabase"
import {
  requirePermission,
  checkRateLimit,
  logUserAction,
  validateSession
} from "../auth"
import {
  handleServerActionError,
  RateLimitError,
  ValidationError
} from "./shared"

export async function saveEmployeeAction(formData: FormData) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'employee-save', 10, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('employee:write')

    // Extract and validate data
    const rawData = {
      id: formData.get('id') as string || undefined,
      fullName: formData.get('fullName') as string,
      email: formData.get('email') as string,
      role: formData.get('role') as string || undefined,
      linkedinUrl: formData.get('linkedinUrl') as string || undefined,
      twitterUrl: formData.get('twitterUrl') as string || undefined,
      telegramUrl: formData.get('telegramUrl') as string || undefined,
      compensation: formData.get('compensation') as 'hourly' | 'monthly',
      rate: Number(formData.get('rate')),
      departmentId: formData.get('departmentId') as string,
      managerId: formData.get('managerId') as string || null,
      active: formData.get('active') === 'true',
    }

    const validatedData = employeeFormSchema.parse(rawData)

    // Log the action
    await logUserAction('employee:save', {
      employeeName: validatedData.fullName,
      isUpdate: !!rawData.id
    })

    // Save to database
    await saveEmployee(validatedData)

    // Revalidate cache
    revalidatePath("/dashboard/employees")

    return { success: true, message: "Employee saved successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function deleteEmployeeAction(employeeId: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'employee-delete', 3, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check (only super-admin and hr-admin can delete)
    await requirePermission('employee:delete')

    // Validate employee ID
    if (!employeeId || typeof employeeId !== 'string') {
      throw new ValidationError("Valid employee ID is required")
    }

    // Log the action
    await logUserAction('employee:delete', { employeeId })

    // Soft delete employee (set active to false)
    await db.softDeleteEmployee(employeeId)

    // Revalidate cache
    revalidatePath("/dashboard/employees")

    return { success: true, message: "Employee deleted successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function assignManagersToEmployeeAction(
  employeeId: string,
  managerIds: string[],
  primaryManagerId?: string
): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    const session = await validateSession()
    await requirePermission('employee:write')

    if (!employeeId || !managerIds || managerIds.length === 0) {
      throw new ValidationError('Employee ID and at least one manager ID required')
    }

    // Remove existing manager assignments
    const existingManagers = await db.getEmployeeManagers(employeeId)
    for (const manager of existingManagers) {
      await db.removeManagerFromEmployee(employeeId, manager.managerId)
    }

    // Add new manager assignments
    for (const managerId of managerIds) {
      const isPrimary = managerId === primaryManagerId
      await db.assignManagerToEmployee(employeeId, managerId, isPrimary)
    }

    await logUserAction('employee:assign-managers', {
      employeeId,
      managerIds,
      primaryManagerId,
      userId: session.userId
    })

    revalidatePath("/dashboard/employees")
    revalidatePath("/dashboard")

    return {
      success: true,
      message: `Successfully assigned ${managerIds.length} managers to employee`
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function updateEmployeeProfile(
  employeeId: string,
  data: {
    first_name?: string | null
    last_name?: string | null
    email?: string | null
    role?: string | null
    bio?: string | null
    linkedin_url?: string | null
    twitter_url?: string | null
    telegram_url?: string | null
  }
) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'employee-profile-update', 10, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check - user can edit their own profile or have permission
    const currentUserId = session.userId
    if (employeeId !== currentUserId) {
      await requirePermission('employee:write')
    }

    // Validate employee ID
    if (!employeeId || typeof employeeId !== 'string') {
      throw new ValidationError("Valid employee ID is required")
    }

    // Import validation utilities
    const { validateProfileData } = await import('../validation')

    // Validate and sanitize the profile data
    const validation = validateProfileData({
      firstName: data.first_name || undefined,
      lastName: data.last_name || undefined,
      email: data.email || undefined,
      role: data.role || undefined,
      bio: data.bio || undefined,
      linkedinUrl: data.linkedin_url || undefined,
      twitterUrl: data.twitter_url || undefined,
      telegramUrl: data.telegram_url || undefined,
    })

    if (!validation.isValid) {
      throw new ValidationError(`Validation failed: ${validation.errors.join(', ')}`)
    }

    // Prepare update data with sanitized values and computed full_name
    const updateData = {
      ...validation.sanitizedData,
      full_name: `${validation.sanitizedData.first_name} ${validation.sanitizedData.last_name}`.trim()
    }

    // Update in database
    const { error } = await supabaseAdminQuery
      .employees()
      .update(updateData)
      .eq('id', employeeId)

    if (error) {
      console.error("Error updating employee profile:", error)
      throw new Error("Failed to update employee profile")
    }

    // Log the action
    await logUserAction('employee:update-profile', { 
      employeeId,
      updatedFields: Object.keys(updateData)
    })

    // Revalidate cache
    revalidatePath(`/dashboard/employees/${employeeId}/profile`)
    revalidatePath('/dashboard/employees')
    
    return { success: true, message: "Profile updated successfully" }
  } catch (error) {
    return handleServerActionError(error)
  }
}