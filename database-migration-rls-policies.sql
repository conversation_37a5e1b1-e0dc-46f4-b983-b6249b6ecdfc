-- Database Migration: Add missing fields and comprehensive RLS policies
-- Run this in your Supabase SQL Editor

-- =====================================================
-- 1. ADD MISSING COLUMNS TO appy_employees
-- =====================================================

ALTER TABLE appy_employees 
ADD COLUMN IF NOT EXISTS role VARCHAR(100),
ADD COLUMN IF NOT EXISTS telegram_url VARCHAR(255);

-- =====================================================
-- 2. ENABLE RLS ON ALL TABLES THAT DON'T HAVE IT
-- =====================================================

ALTER TABLE appy_appraisal_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_email_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_employee_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_employee_kpis ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_employee_pto_balances ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_feedback_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_feedback_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_feedback_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_notification_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_pto_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_scheduled_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_template_usage ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 3. DROP EXISTING POLICIES THAT NEED TO BE UPDATED
-- =====================================================

-- Drop existing KPI policies to recreate them properly
DROP POLICY IF EXISTS "Employees can manage own KPIs" ON appy_employee_kpis;
DROP POLICY IF EXISTS "Managers can manage team KPIs" ON appy_employee_kpis;

-- =====================================================
-- 4. COMPREHENSIVE RLS POLICIES
-- =====================================================

-- appy_appraisal_templates policies
CREATE POLICY "Admins can manage all templates" ON appy_appraisal_templates
  FOR ALL USING (is_admin());

CREATE POLICY "Managers can read templates" ON appy_appraisal_templates
  FOR SELECT USING (get_user_role() = 'manager' AND is_active = true);

-- appy_employee_kpis policies (recreated)
CREATE POLICY "Admins can manage all KPIs" ON appy_employee_kpis
  FOR ALL USING (is_admin());

CREATE POLICY "Managers can manage team KPIs" ON appy_employee_kpis
  FOR ALL USING (
    get_user_role() = 'manager' AND
    EXISTS (
      SELECT 1 FROM appy_employees e 
      WHERE e.id = employee_id AND e.manager_id = auth.uid()::text
    )
  );

CREATE POLICY "Employees can read own KPIs" ON appy_employee_kpis
  FOR SELECT USING (employee_id = auth.uid());

CREATE POLICY "Accountants can read all KPIs" ON appy_employee_kpis
  FOR SELECT USING (get_user_role() = 'accountant');

-- appy_employee_pto_balances policies
CREATE POLICY "Admins can manage all PTO balances" ON appy_employee_pto_balances
  FOR ALL USING (is_admin());

CREATE POLICY "Managers can read team PTO balances" ON appy_employee_pto_balances
  FOR SELECT USING (
    get_user_role() = 'manager' AND
    EXISTS (
      SELECT 1 FROM appy_employees e 
      WHERE e.id = employee_id AND e.manager_id = auth.uid()::text
    )
  );

CREATE POLICY "Employees can read own PTO balance" ON appy_employee_pto_balances
  FOR SELECT USING (employee_id = auth.uid());

CREATE POLICY "Accountants can read all PTO balances" ON appy_employee_pto_balances
  FOR SELECT USING (get_user_role() = 'accountant');

-- appy_pto_requests policies
CREATE POLICY "Admins can manage all PTO requests" ON appy_pto_requests
  FOR ALL USING (is_admin());

CREATE POLICY "Managers can manage team PTO requests" ON appy_pto_requests
  FOR ALL USING (
    get_user_role() = 'manager' AND manager_id = auth.uid()::text
  );

CREATE POLICY "Employees can manage own PTO requests" ON appy_pto_requests
  FOR ALL USING (employee_id = auth.uid());

CREATE POLICY "Accountants can read all PTO requests" ON appy_pto_requests
  FOR SELECT USING (get_user_role() = 'accountant');

-- appy_employee_feedback policies
CREATE POLICY "Admins can manage all feedback" ON appy_employee_feedback
  FOR ALL USING (is_admin());

CREATE POLICY "Employees can create feedback" ON appy_employee_feedback
  FOR INSERT WITH CHECK (submitter_id = auth.uid());

CREATE POLICY "Employees can read own submitted feedback" ON appy_employee_feedback
  FOR SELECT USING (submitter_id = auth.uid() AND NOT is_anonymous);

CREATE POLICY "Managers can read feedback about their team" ON appy_employee_feedback
  FOR SELECT USING (
    get_user_role() = 'manager' AND
    EXISTS (
      SELECT 1 FROM appy_employees e 
      WHERE e.id = target_employee_id AND e.manager_id = auth.uid()::text
    )
  );

-- appy_feedback_comments policies
CREATE POLICY "Admins can manage all feedback comments" ON appy_feedback_comments
  FOR ALL USING (is_admin());

CREATE POLICY "Users can read comments on accessible feedback" ON appy_feedback_comments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM appy_employee_feedback f 
      WHERE f.id = feedback_id AND (
        f.submitter_id = auth.uid() OR
        is_admin() OR
        (get_user_role() = 'manager' AND EXISTS (
          SELECT 1 FROM appy_employees e 
          WHERE e.id = f.target_employee_id AND e.manager_id = auth.uid()::text
        ))
      )
    )
  );

-- appy_feedback_attachments policies
CREATE POLICY "Admins can manage all feedback attachments" ON appy_feedback_attachments
  FOR ALL USING (is_admin());

CREATE POLICY "Users can read attachments on accessible feedback" ON appy_feedback_attachments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM appy_employee_feedback f 
      WHERE f.id = feedback_id AND (
        f.submitter_id = auth.uid() OR
        is_admin() OR
        (get_user_role() = 'manager' AND EXISTS (
          SELECT 1 FROM appy_employees e 
          WHERE e.id = f.target_employee_id AND e.manager_id = auth.uid()::text
        ))
      )
    )
  );

-- appy_feedback_status_history policies
CREATE POLICY "Admins can manage all feedback status history" ON appy_feedback_status_history
  FOR ALL USING (is_admin());

CREATE POLICY "Users can read status history on accessible feedback" ON appy_feedback_status_history
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM appy_employee_feedback f
      WHERE f.id = feedback_id AND (
        f.submitter_id = auth.uid() OR
        is_admin() OR
        (get_user_role() = 'manager' AND EXISTS (
          SELECT 1 FROM appy_employees e
          WHERE e.id = f.target_employee_id AND e.manager_id = auth.uid()::text
        ))
      )
    )
  );

-- appy_audit_log policies
CREATE POLICY "Admins can read all audit logs" ON appy_audit_log
  FOR SELECT USING (is_admin());

-- appy_email_settings policies
CREATE POLICY "Admins can manage email settings" ON appy_email_settings
  FOR ALL USING (is_admin());

-- appy_email_templates policies
CREATE POLICY "Admins can manage email templates" ON appy_email_templates
  FOR ALL USING (is_admin());

CREATE POLICY "Managers can read active email templates" ON appy_email_templates
  FOR SELECT USING (get_user_role() = 'manager' AND is_active = true);

-- appy_notification_log policies
CREATE POLICY "Admins can manage notification logs" ON appy_notification_log
  FOR ALL USING (is_admin());

CREATE POLICY "Users can read their own notifications" ON appy_notification_log
  FOR SELECT USING (
    recipient_email = (
      SELECT email FROM appy_managers WHERE user_id = auth.uid()::text
      UNION
      SELECT email FROM appy_employees WHERE id = auth.uid()
    )
  );

-- appy_scheduled_notifications policies
CREATE POLICY "Admins can manage scheduled notifications" ON appy_scheduled_notifications
  FOR ALL USING (is_admin());

CREATE POLICY "Users can read their own scheduled notifications" ON appy_scheduled_notifications
  FOR SELECT USING (recipient_id = auth.uid()::text);

-- appy_template_usage policies
CREATE POLICY "Admins can read all template usage" ON appy_template_usage
  FOR SELECT USING (is_admin());

CREATE POLICY "Managers can read own template usage" ON appy_template_usage
  FOR SELECT USING (manager_id = auth.uid()::text);

-- =====================================================
-- 5. UPDATE EXISTING POLICIES FOR BETTER HIERARCHY SUPPORT
-- =====================================================

-- Update employees policy to support hierarchical access for super-admins
DROP POLICY IF EXISTS "Managers can read their employees" ON appy_employees;

CREATE POLICY "Managers can read their employees and hierarchy" ON appy_employees
  FOR SELECT USING (
    (get_user_role() = 'manager' AND manager_id = auth.uid()::text) OR
    (get_user_role() = 'super-admin' AND EXISTS (
      -- Super-admins can see employees managed by people they manage (hierarchical)
      SELECT 1 FROM appy_employees managed_manager
      WHERE managed_manager.manager_id = auth.uid()::text
      AND appy_employees.manager_id = managed_manager.id::text
    ))
  );

-- Update appraisals policy for hierarchical access
DROP POLICY IF EXISTS "Managers can manage their appraisals" ON appy_appraisals;

CREATE POLICY "Managers can manage their appraisals and hierarchy" ON appy_appraisals
  FOR ALL USING (
    (get_user_role() = 'manager' AND manager_id = auth.uid()::text) OR
    (get_user_role() = 'super-admin' AND EXISTS (
      -- Super-admins can see appraisals for employees in their hierarchy
      SELECT 1 FROM appy_employees e, appy_employees managed_manager
      WHERE e.id = appy_appraisals.employee_id
      AND managed_manager.manager_id = auth.uid()::text
      AND e.manager_id = managed_manager.id::text
    ))
  );

-- =====================================================
-- 6. CONSOLE LOGGING FOR DEBUGGING
-- =====================================================

-- Add a function to log RLS policy access for debugging
CREATE OR REPLACE FUNCTION log_rls_access(table_name text, action text, user_id text)
RETURNS void AS $$
BEGIN
  -- Only log in development/debug mode
  IF current_setting('app.debug_mode', true) = 'true' THEN
    INSERT INTO appy_audit_log (action, table_name, record_id, user_id, new_values)
    VALUES ('RLS_ACCESS', table_name, null, user_id, jsonb_build_object('action', action, 'timestamp', now()));
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 7. VERIFICATION QUERIES
-- =====================================================

-- Run these queries to verify the setup:
/*
-- Check RLS status on all tables
SELECT schemaname, tablename, rowsecurity
FROM pg_tables
WHERE schemaname = 'public' AND tablename LIKE 'appy_%'
ORDER BY tablename;

-- Check all policies
SELECT schemaname, tablename, policyname, cmd, roles
FROM pg_policies
WHERE schemaname = 'public' AND tablename LIKE 'appy_%'
ORDER BY tablename, policyname;

-- Test user role function
SELECT get_user_role(), is_admin();

-- Test employee hierarchy access
SELECT e.full_name, e.manager_id, m.full_name as manager_name
FROM appy_employees e
LEFT JOIN appy_managers m ON e.manager_id = m.user_id
WHERE e.active = true;
*/
